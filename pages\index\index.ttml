<view class="container">
  <!-- 顶部标题 -->
  <view class="header">
    <text class="title">日期计算器</text>
    <text class="subtitle">专业的日期计算工具</text>
  </view>

  <!-- 功能选择卡片 -->
  <view class="function-cards">
    <!-- 日期间隔计算 -->
    <view class="card function-card" bindtap="selectFunction" data-type="interval">
      <view class="card-icon">📅</view>
      <view class="card-content">
        <text class="card-title">日期间隔</text>
        <text class="card-desc">计算两个日期之间的天数、月数、年数</text>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 日期加减运算 -->
    <view class="card function-card" bindtap="selectFunction" data-type="calculate">
      <view class="card-icon">🧮</view>
      <view class="card-content">
        <text class="card-title">日期加减</text>
        <text class="card-desc">在指定日期基础上加减天数、月数、年数</text>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 工作日计算 -->
    <view class="card function-card" bindtap="selectFunction" data-type="workday">
      <view class="card-icon">💼</view>
      <view class="card-content">
        <text class="card-title">工作日计算</text>
        <text class="card-desc">排除周末和节假日的工作日计算</text>
      </view>
      <view class="card-arrow">→</view>
    </view>

    <!-- 节假日查询 -->
    <view class="card function-card" bindtap="selectFunction" data-type="holiday">
      <view class="card-icon">🎉</view>
      <view class="card-content">
        <text class="card-title">节假日查询</text>
        <text class="card-desc">查询法定节假日和调休安排</text>
      </view>
      <view class="card-arrow">→</view>
    </view>
  </view>

<!-- 广告解锁弹窗 -->
<view class="ad-modal" wx:if="{{showAdModal}}">
  <view class="ad-modal-mask" bindtap="closeAdModal"></view>
  <view class="ad-modal-content">
    <view class="ad-header">
      <text class="ad-title">解锁高级功能</text>
      <text class="ad-subtitle">观看完整广告后可使用工作日计算和节假日查询功能</text>
    </view>
    
    <view class="ad-benefits">
      <view class="benefit-item">
        <text class="benefit-icon">✅</text>
        <text class="benefit-text">工作日精确计算</text>
      </view>
      <view class="benefit-item">
        <text class="benefit-icon">✅</text>
        <text class="benefit-text">节假日智能识别</text>
      </view>
      <view class="benefit-item">
        <text class="benefit-icon">✅</text>
        <text class="benefit-text">调休安排查询</text>
      </view>
    </view>
    
    <view class="ad-buttons">
      <button class="btn btn-primary" bindtap="watchAd">观看广告解锁</button>
      <button class="btn btn-secondary" bindtap="closeAdModal">暂不解锁</button>
    </view>
  </view>
</view>

<!-- 历史记录弹窗 -->
<view class="history-modal" wx:if="{{showHistoryModal}}">
  <view class="history-modal-mask" bindtap="closeHistoryModal"></view>
  <view class="history-modal-content">
    <view class="history-header">
      <text class="history-title">计算历史</text>
      <view class="history-actions">
        <text class="clear-history-btn" bindtap="clearHistory">清空</text>
        <text class="close-btn" bindtap="closeHistoryModal">✕</text>
      </view>
    </view>
    
    <scroll-view class="history-list-full" scroll-y="true">
      <view class="history-item-full" wx:for="{{allHistoryList}}" wx:key="id" bindtap="useHistory" data-item="{{item}}">
        <view class="history-content-full">
          <text class="history-title-full">{{item.title}}</text>
          <text class="history-desc-full">{{item.description}}</text>
          <text class="history-result-full">{{item.result}}</text>
          <text class="history-time-full">{{item.formatTime}}</text>
        </view>
        <view class="history-delete" bindtap="deleteHistory" data-id="{{item.id}}" catchtap="true">
          <text class="delete-icon">🗑️</text>
        </view>
      </view>
      
      <view class="empty-history" wx:if="{{allHistoryList.length === 0}}">
        <text class="empty-icon">📝</text>
        <text class="empty-title">暂无计算记录</text>
        <text class="empty-description">开始使用日期计算功能，记录会自动保存在这里</text>
      </view>
    </scroll-view>
  </view>
</view>