/* 全局样式 */
page {
  background-color: #F8FAFC;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  color: #1E293B;
  line-height: 1.6;
}

/* 通用按钮样式 */
.btn {
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
  padding: 12px 24px;
  margin: 8px 0;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn:active::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: #FFFFFF;
  color: #3B82F6;
  border: 2px solid #3B82F6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary:active {
  background: #F1F5F9;
  transform: translateY(1px);
}

.btn-success {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-small {
  font-size: 14px;
  min-height: 36px;
  padding: 8px 16px;
}

.btn-large {
  font-size: 18px;
  min-height: 52px;
  padding: 16px 32px;
}

.btn:disabled {
  opacity: 0.6;
  transform: none !important;
  box-shadow: none !important;
}

/* 通用卡片样式 */
.card {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin: 12px 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:active {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #E2E8F0;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
  margin: 0;
}

.card-subtitle {
  font-size: 14px;
  color: #64748B;
  margin: 4px 0 0 0;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 16px 20px;
  background: #F8FAFC;
  border-top: 1px solid #E2E8F0;
}

/* 通用输入框样式 */
.input-group {
  margin: 16px 0;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.input-field {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #E2E8F0;
  border-radius: 8px;
  font-size: 16px;
  color: #1E293B;
  background: #FFFFFF;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.input-field.error {
  border-color: #EF4444;
}

.input-error {
  font-size: 12px;
  color: #EF4444;
  margin-top: 4px;
}

/* 通用列表样式 */
.list {
  background: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-item {
  padding: 16px 20px;
  border-bottom: 1px solid #F1F5F9;
  transition: background 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background: #F8FAFC;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 16px;
  font-weight: 500;
  color: #1E293B;
  margin-bottom: 4px;
}

.list-item-subtitle {
  font-size: 14px;
  color: #64748B;
}

.list-item-action {
  color: #3B82F6;
  font-size: 14px;
}

/* 通用标签样式 */
.tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  margin: 2px;
}

.tag-primary {
  background: rgba(59, 130, 246, 0.1);
  color: #3B82F6;
}

.tag-success {
  background: rgba(16, 185, 129, 0.1);
  color: #10B981;
}

.tag-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #F59E0B;
}

.tag-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #EF4444;
}

/* 通用加载样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #64748B;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #E2E8F0;
  border-top: 2px solid #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 通用空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #9CA3AF;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #6B7280;
}

.empty-description {
  font-size: 14px;
  line-height: 1.5;
}

/* 通用分割线 */
.divider {
  height: 1px;
  background: #E2E8F0;
  margin: 20px 0;
}

.divider-text {
  position: relative;
  text-align: center;
  margin: 20px 0;
  color: #64748B;
  font-size: 14px;
}

.divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #E2E8F0;
  z-index: 1;
}

.divider-text text {
  background: #F8FAFC;
  padding: 0 16px;
  position: relative;
  z-index: 2;
}

/* 响应式工具类 */
.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.col {
  flex: 1;
  padding: 0 10px;
}

.col-2 {
  flex: 0 0 50%;
  padding: 0 10px;
}

.col-3 {
  flex: 0 0 33.333%;
  padding: 0 10px;
}

.col-4 {
  flex: 0 0 25%;
  padding: 0 10px;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: #3B82F6; }
.text-success { color: #10B981; }
.text-warning { color: #F59E0B; }
.text-danger { color: #EF4444; }
.text-muted { color: #64748B; }

.bg-primary { background-color: #3B82F6; }
.bg-success { background-color: #10B981; }
.bg-warning { background-color: #F59E0B; }
.bg-danger { background-color: #EF4444; }
.bg-light { background-color: #F8FAFC; }

.m-0 { margin: 0; }
.m-1 { margin: 8px; }
.m-2 { margin: 16px; }
.m-3 { margin: 24px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }

.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.pt-1 { padding-top: 8px; }
.pt-2 { padding-top: 16px; }
.pt-3 { padding-top: 24px; }

.pb-1 { padding-bottom: 8px; }
.pb-2 { padding-bottom: 16px; }
.pb-3 { padding-bottom: 24px; }

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bounce-in {
  animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}