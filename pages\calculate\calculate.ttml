<view class="container">
  <!-- 日期间隔计算 -->
  <view class="calc-section" wx:if="{{calcType === 'interval'}}">
    <view class="section-title">
      <text class="title-text">日期间隔计算</text>
      <text class="title-desc">计算两个日期之间的时间间隔</text>
    </view>
    
    <view class="input-group">
      <text class="input-label">开始日期</text>
      <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
        <view class="date-picker">
          <text class="date-text">{{startDate || '请选择开始日期'}}</text>
          <text class="picker-icon">📅</text>
        </view>
      </picker>
    </view>
    
    <view class="input-group">
      <text class="input-label">结束日期</text>
      <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
        <view class="date-picker">
          <text class="date-text">{{endDate || '请选择结束日期'}}</text>
          <text class="picker-icon">📅</text>
        </view>
      </picker>
    </view>
    
    <button class="btn btn-primary" bindtap="calculateInterval">计算间隔</button>
    
    <view class="result-section" wx:if="{{intervalResult}}">
      <view class="result-card">
        <view class="result-header">
          <text class="result-title">计算结果</text>
        </view>
        <view class="result-content">
          <view class="result-item">
            <text class="result-label">总天数</text>
            <text class="result-value">{{intervalResult.totalDays}}天</text>
          </view>
          <view class="result-item">
            <text class="result-label">年月日</text>
            <text class="result-value">{{intervalResult.years}}年{{intervalResult.months}}月{{intervalResult.days}}天</text>
          </view>
          <view class="result-item">
            <text class="result-label">总周数</text>
            <text class="result-value">{{intervalResult.totalWeeks}}周</text>
          </view>
          <view class="result-item">
            <text class="result-label">总小时</text>
            <text class="result-value">{{intervalResult.totalHours}}小时</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 日期加减运算 -->
  <view class="calc-section" wx:if="{{calcType === 'calculate'}}">
    <view class="section-title">
      <text class="title-text">日期加减运算</text>
      <text class="title-desc">在指定日期基础上加减时间</text>
    </view>
    
    <view class="input-group">
      <text class="input-label">基准日期</text>
      <picker mode="date" value="{{baseDate}}" bindchange="onBaseDateChange">
        <view class="date-picker">
          <text class="date-text">{{baseDate || '请选择基准日期'}}</text>
          <text class="picker-icon">📅</text>
        </view>
      </picker>
    </view>
    
    <view class="operation-group">
      <view class="operation-tabs">
        <view class="tab-item {{operation === 'add' ? 'active' : ''}}" bindtap="setOperation" data-op="add">
          <text class="tab-text">加上</text>
        </view>
        <view class="tab-item {{operation === 'subtract' ? 'active' : ''}}" bindtap="setOperation" data-op="subtract">
          <text class="tab-text">减去</text>
        </view>
      </view>
    </view>
    
    <view class="time-inputs">
      <view class="time-input-group">
        <text class="time-label">年</text>
        <input class="time-input" type="number" value="{{addYears}}" bindinput="onAddYearsInput" placeholder="0"/>
      </view>
      <view class="time-input-group">
        <text class="time-label">月</text>
        <input class="time-input" type="number" value="{{addMonths}}" bindinput="onAddMonthsInput" placeholder="0"/>
      </view>
      <view class="time-input-group">
        <text class="time-label">天</text>
        <input class="time-input" type="number" value="{{addDays}}" bindinput="onAddDaysInput" placeholder="0"/>
      </view>
    </view>
    
    <button class="btn btn-primary" bindtap="calculateDate">计算日期</button>
    
    <view class="result-section" wx:if="{{dateResult}}">
      <view class="result-card">
        <view class="result-header">
          <text class="result-title">计算结果</text>
        </view>
        <view class="result-content">
          <view class="result-item">
            <text class="result-label">结果日期</text>
            <text class="result-value">{{dateResult.resultDate}}</text>
          </view>
          <view class="result-item">
            <text class="result-label">星期</text>
            <text class="result-value">{{dateResult.weekday}}</text>
          </view>
          <view class="result-item">
            <text class="result-label">距今</text>
            <text class="result-value">{{dateResult.fromNow}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 工作日计算 -->
  <view class="calc-section" wx:if="{{calcType === 'workday'}}">
    <view class="section-title">
      <text class="title-text">工作日计算</text>
      <text class="title-desc">排除周末和节假日的工作日计算</text>
    </view>
    
    <view class="premium-notice" wx:if="{{!isPremium}}">
      <view class="notice-icon">🔒</view>
      <text class="notice-text">此功能需要观看广告解锁</text>
      <button class="btn btn-warning btn-small" bindtap="unlockPremium">解锁功能</button>
    </view>
    
    <view wx:if="{{isPremium}}">
      <view class="input-group">
        <text class="input-label">开始日期</text>
        <picker mode="date" value="{{workStartDate}}" bindchange="onWorkStartDateChange">
          <view class="date-picker">
            <text class="date-text">{{workStartDate || '请选择开始日期'}}</text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>
      
      <view class="input-group">
        <text class="input-label">结束日期</text>
        <picker mode="date" value="{{workEndDate}}" bindchange="onWorkEndDateChange">
          <view class="date-picker">
            <text class="date-text">{{workEndDate || '请选择结束日期'}}</text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>
      
      <view class="options-group">
        <view class="option-item">
          <text class="option-label">排除周末</text>
          <switch checked="{{excludeWeekends}}" bindchange="onExcludeWeekendsChange"/>
        </view>
        <view class="option-item">
          <text class="option-label">排除节假日</text>
          <switch checked="{{excludeHolidays}}" bindchange="onExcludeHolidaysChange"/>
        </view>
      </view>
      
      <button class="btn btn-primary" bindtap="calculateWorkdays">计算工作日</button>
      
      <view class="result-section" wx:if="{{workdayResult}}">
        <view class="result-card">
          <view class="result-header">
            <text class="result-title">工作日统计</text>
          </view>
          <view class="result-content">
            <view class="result-item">
              <text class="result-label">总工作日</text>
              <text class="result-value">{{workdayResult.totalWorkdays}}天</text>
            </view>
            <view class="result-item">
              <text class="result-label">周末天数</text>
              <text class="result-value">{{workdayResult.weekendDays}}天</text>
            </view>
            <view class="result-item">
              <text class="result-label">节假日</text>
              <text class="result-value">{{workdayResult.holidayDays}}天</text>
            </view>
            <view class="result-item">
              <text class="result-label">工作周数</text>
              <text class="result-value">{{workdayResult.workWeeks}}周</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 节假日查询 -->
  <view class="calc-section" wx:if="{{calcType === 'holiday'}}">
    <view class="section-title">
      <text class="title-text">节假日查询</text>
      <text class="title-desc">查询法定节假日和调休安排</text>
    </view>
    
    <view class="premium-notice" wx:if="{{!isPremium}}">
      <view class="notice-icon">🔒</view>
      <text class="notice-text">此功能需要观看广告解锁</text>
      <button class="btn btn-warning btn-small" bindtap="unlockPremium">解锁功能</button>
    </view>
    
    <view wx:if="{{isPremium}}">
      <view class="input-group">
        <text class="input-label">查询年份</text>
        <picker mode="date" fields="year" value="{{queryYear}}" bindchange="onQueryYearChange">
          <view class="date-picker">
            <text class="date-text">{{queryYear || '请选择年份'}}</text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>
      
      <button class="btn btn-primary" bindtap="queryHolidays">查询节假日</button>
      
      <view class="holiday-list" wx:if="{{holidayList.length > 0}}">
        <view class="holiday-item" wx:for="{{holidayList}}" wx:key="id">
          <view class="holiday-info">
            <text class="holiday-name">{{item.name}}</text>
            <text class="holiday-date">{{item.date}}</text>
            <text class="holiday-type">{{item.type}}</text>
          </view>
          <view class="holiday-days">
            <text class="days-text">{{item.days}}天</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>