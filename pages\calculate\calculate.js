const app = getApp();

Page({
  data: {
    calcType: '',
    pageTitle: '',
    
    // 日期间隔计算
    startDate: '',
    endDate: '',
    intervalResult: null,
    
    // 日期加减运算
    baseDate: '',
    operation: 'add',
    addYears: 0,
    addMonths: 0,
    addDays: 0,
    dateResult: null,
    
    // 工作日计算
    workStartDate: '',
    workEndDate: '',
    excludeWeekends: true,
    excludeHolidays: true,
    workdayResult: null,
    
    // 节假日查询
    queryYear: '',
    holidayList: [],
    
    // 权限控制
    isPremium: false
  },

  onLoad(options) {
    const calcType = options.type || 'interval';
    const titles = {
      interval: '日期间隔计算',
      calculate: '日期加减运算',
      workday: '工作日计算',
      holiday: '节假日查询'
    };
    
    // 检查高级功能权限
    const userData = app.globalData.userData || {};
    const isPremium = userData.premiumUnlocked || false;
    
    this.setData({
      calcType: calcType,
      pageTitle: titles[calcType] || '日期计算',
      isPremium: isPremium
    });
    
    // 初始化默认日期
    this.initDefaultDates();
  },

  // 初始化默认日期
  initDefaultDates() {
    const today = new Date();
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    const todayStr = this.formatDate(today);
    const tomorrowStr = this.formatDate(tomorrow);
    
    this.setData({
      startDate: todayStr,
      baseDate: todayStr,
      workStartDate: todayStr,
      endDate: tomorrowStr,
      workEndDate: tomorrowStr,
      queryYear: today.getFullYear().toString()
    });
  },

  // 返回上一页
  goBack() {
    tt.navigateBack();
  },

  // 显示历史记录
  showHistory() {
    tt.showModal({
      title: '计算历史',
      content: '查看最近的计算记录',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 日期间隔计算相关方法
  onStartDateChange(e) {
    this.setData({
      startDate: e.detail.value
    });
  },

  onEndDateChange(e) {
    this.setData({
      endDate: e.detail.value
    });
  },

  calculateInterval() {
    const { startDate, endDate } = this.data;
    
    if (!startDate || !endDate) {
      tt.showToast({
        title: '请选择日期',
        icon: 'none'
      });
      return;
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start >= end) {
      tt.showToast({
        title: '结束日期应晚于开始日期',
        icon: 'none'
      });
      return;
    }
    
    const result = this.calculateDateInterval(start, end);
    
    this.setData({
      intervalResult: result
    });
    
    // 记录计算历史
    this.recordCalculation({
      type: 'interval',
      title: '日期间隔计算',
      description: `${startDate} 到 ${endDate}`,
      result: `相差 ${result.totalDays} 天`
    });
    
    // 显示插屏广告
    if (app.shouldShowAd()) {
      app.showInterstitialAd();
    }
  },

  // 计算日期间隔
  calculateDateInterval(start, end) {
    const timeDiff = end.getTime() - start.getTime();
    const totalDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
    const totalHours = Math.ceil(timeDiff / (1000 * 3600));
    const totalWeeks = Math.floor(totalDays / 7);
    
    // 计算年月日差
    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();
    
    if (days < 0) {
      months--;
      const lastMonth = new Date(end.getFullYear(), end.getMonth(), 0);
      days += lastMonth.getDate();
    }
    
    if (months < 0) {
      years--;
      months += 12;
    }
    
    return {
      totalDays,
      totalHours,
      totalWeeks,
      years,
      months,
      days
    };
  },

  // 日期加减运算相关方法
  onBaseDateChange(e) {
    this.setData({
      baseDate: e.detail.value
    });
  },

  setOperation(e) {
    this.setData({
      operation: e.currentTarget.dataset.op
    });
  },

  onAddYearsInput(e) {
    this.setData({
      addYears: parseInt(e.detail.value) || 0
    });
  },

  onAddMonthsInput(e) {
    this.setData({
      addMonths: parseInt(e.detail.value) || 0
    });
  },

  onAddDaysInput(e) {
    this.setData({
      addDays: parseInt(e.detail.value) || 0
    });
  },

  calculateDate() {
    const { baseDate, operation, addYears, addMonths, addDays } = this.data;
    
    if (!baseDate) {
      tt.showToast({
        title: '请选择基准日期',
        icon: 'none'
      });
      return;
    }
    
    if (addYears === 0 && addMonths === 0 && addDays === 0) {
      tt.showToast({
        title: '请输入要加减的时间',
        icon: 'none'
      });
      return;
    }
    
    const base = new Date(baseDate);
    const result = new Date(base);
    
    const multiplier = operation === 'add' ? 1 : -1;
    
    result.setFullYear(result.getFullYear() + addYears * multiplier);
    result.setMonth(result.getMonth() + addMonths * multiplier);
    result.setDate(result.getDate() + addDays * multiplier);
    
    const resultDate = this.formatDate(result);
    const weekday = this.getWeekday(result);
    const fromNow = this.getFromNow(result);
    
    this.setData({
      dateResult: {
        resultDate,
        weekday,
        fromNow
      }
    });
    
    // 记录计算历史
    const opText = operation === 'add' ? '加上' : '减去';
    this.recordCalculation({
      type: 'calculate',
      title: '日期加减运算',
      description: `${baseDate} ${opText} ${addYears}年${addMonths}月${addDays}天`,
      result: `结果：${resultDate}`
    });
    
    // 显示插屏广告
    if (app.shouldShowAd()) {
      app.showInterstitialAd();
    }
  },

  // 工作日计算相关方法
  onWorkStartDateChange(e) {
    this.setData({
      workStartDate: e.detail.value
    });
  },

  onWorkEndDateChange(e) {
    this.setData({
      workEndDate: e.detail.value
    });
  },

  onExcludeWeekendsChange(e) {
    this.setData({
      excludeWeekends: e.detail.value
    });
  },

  onExcludeHolidaysChange(e) {
    this.setData({
      excludeHolidays: e.detail.value
    });
  },

  calculateWorkdays() {
    const { workStartDate, workEndDate, excludeWeekends, excludeHolidays } = this.data;
    
    if (!workStartDate || !workEndDate) {
      tt.showToast({
        title: '请选择日期',
        icon: 'none'
      });
      return;
    }
    
    const start = new Date(workStartDate);
    const end = new Date(workEndDate);
    
    if (start >= end) {
      tt.showToast({
        title: '结束日期应晚于开始日期',
        icon: 'none'
      });
      return;
    }
    
    const result = this.calculateWorkdayInterval(start, end, excludeWeekends, excludeHolidays);
    
    this.setData({
      workdayResult: result
    });
    
    // 记录计算历史
    this.recordCalculation({
      type: 'workday',
      title: '工作日计算',
      description: `${workStartDate} 到 ${workEndDate}`,
      result: `工作日：${result.totalWorkdays} 天`
    });
  },

  // 计算工作日间隔
  calculateWorkdayInterval(start, end, excludeWeekends, excludeHolidays) {
    let totalDays = 0;
    let weekendDays = 0;
    let holidayDays = 0;
    let workdays = 0;
    
    const current = new Date(start);
    
    while (current <= end) {
      totalDays++;
      
      const dayOfWeek = current.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      const isHoliday = this.isHoliday(current);
      
      if (isWeekend && excludeWeekends) {
        weekendDays++;
      } else if (isHoliday && excludeHolidays) {
        holidayDays++;
      } else {
        workdays++;
      }
      
      current.setDate(current.getDate() + 1);
    }
    
    return {
      totalWorkdays: workdays,
      weekendDays: weekendDays,
      holidayDays: holidayDays,
      workWeeks: Math.floor(workdays / 5)
    };
  },

  // 节假日查询相关方法
  onQueryYearChange(e) {
    this.setData({
      queryYear: e.detail.value
    });
  },

  queryHolidays() {
    const { queryYear } = this.data;
    
    if (!queryYear) {
      tt.showToast({
        title: '请选择年份',
        icon: 'none'
      });
      return;
    }
    
    const holidays = this.getHolidayList(parseInt(queryYear));
    
    this.setData({
      holidayList: holidays
    });
    
    // 记录计算历史
    this.recordCalculation({
      type: 'holiday',
      title: '节假日查询',
      description: `${queryYear}年节假日`,
      result: `共${holidays.length}个节假日`
    });
  },

  // 获取节假日列表
  getHolidayList(year) {
    // 模拟节假日数据
    return [
      {
        id: 1,
        name: '元旦',
        date: `${year}-01-01`,
        type: '法定节假日',
        days: 3
      },
      {
        id: 2,
        name: '春节',
        date: `${year}-02-10`,
        type: '法定节假日',
        days: 7
      },
      {
        id: 3,
        name: '清明节',
        date: `${year}-04-05`,
        type: '法定节假日',
        days: 3
      },
      {
        id: 4,
        name: '劳动节',
        date: `${year}-05-01`,
        type: '法定节假日',
        days: 5
      },
      {
        id: 5,
        name: '端午节',
        date: `${year}-06-22`,
        type: '法定节假日',
        days: 3
      },
      {
        id: 6,
        name: '中秋节',
        date: `${year}-09-29`,
        type: '法定节假日',
        days: 3
      },
      {
        id: 7,
        name: '国庆节',
        date: `${year}-10-01`,
        type: '法定节假日',
        days: 7
      }
    ];
  },

  // 解锁高级功能
  unlockPremium() {
    const self = this;
    
    app.showRewardedVideoAd((success) => {
      if (success) {
        self.setData({
          isPremium: true
        });
        
        tt.showToast({
          title: '功能已解锁',
          icon: 'success'
        });
      }
    });
  },

  // 显示横幅广告
  showBannerAd() {
    app.showBannerAd();
    
    tt.showToast({
      title: '感谢支持！',
      icon: 'success'
    });
  },

  // 工具方法
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  getWeekday(date) {
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return weekdays[date.getDay()];
  },

  getFromNow(date) {
    const now = new Date();
    const diff = date.getTime() - now.getTime();
    const days = Math.ceil(diff / (1000 * 3600 * 24));
    
    if (days === 0) {
      return '今天';
    } else if (days === 1) {
      return '明天';
    } else if (days === -1) {
      return '昨天';
    } else if (days > 0) {
      return `${days}天后`;
    } else {
      return `${Math.abs(days)}天前`;
    }
  },

  isHoliday(date) {
    // 简单的节假日判断逻辑
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    // 元旦
    if (month === 1 && day === 1) return true;
    // 劳动节
    if (month === 5 && day === 1) return true;
    // 国庆节
    if (month === 10 && (day >= 1 && day <= 3)) return true;
    
    return false;
  },

  // 记录计算历史
  recordCalculation(calculation) {
    app.addCalculationHistory(calculation);
  }
});