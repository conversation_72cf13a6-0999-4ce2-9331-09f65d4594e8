const app = getApp();

Page({
  data: {
    // 日期信息
    todayDate: '',
    tomorrowDate: '',
    weekNumber: 0,
    monthDays: 0,
    
    // 历史记录
    historyList: [],
    allHistoryList: [],
    showHistoryModal: false,
    
    // 统计信息
    totalCalculations: 0,
    todayCalculations: 0,
    savedDays: 0,
    
    // 广告相关
    showAdModal: false,
    selectedFunction: null
  },

  onLoad() {
    this.initDateInfo();
    this.loadUserData();
    this.loadHistory();
    
    // 显示横幅广告
    app.showBannerAd();
  },

  onShow() {
    this.loadUserData();
    this.loadHistory();

    // 显示横幅广告
    app.showBannerAd();
  },

  onHide() {
    // 隐藏横幅广告
    app.hideBannerAd();
  },

  // 初始化日期信息
  initDateInfo() {
    const today = new Date();
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    
    // 格式化日期
    const formatDate = (date) => {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    };
    
    // 获取周数
    const getWeekNumber = (date) => {
      const firstDay = new Date(date.getFullYear(), 0, 1);
      const pastDaysOfYear = (date - firstDay) / 86400000;
      return Math.ceil((pastDaysOfYear + firstDay.getDay() + 1) / 7);
    };
    
    // 获取月份天数
    const getMonthDays = (date) => {
      return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
    };
    
    this.setData({
      todayDate: formatDate(today),
      tomorrowDate: formatDate(tomorrow),
      weekNumber: getWeekNumber(today),
      monthDays: getMonthDays(today)
    });
  },

  // 加载用户数据
  loadUserData() {
    const userData = app.globalData.userData || {};
    const today = new Date().toDateString();
    const lastCalculationDate = tt.getStorageSync('dateCalc_lastCalculationDate') || '';
    
    let todayCalculations = 0;
    if (lastCalculationDate === today) {
      todayCalculations = tt.getStorageSync('dateCalc_todayCalculations') || 0;
    }
    
    this.setData({
      totalCalculations: userData.totalCalculations || 0,
      todayCalculations: todayCalculations,
      savedDays: Math.floor((userData.totalCalculations || 0) * 2.5) // 每次计算节省2.5分钟
    });
  },

  // 加载历史记录
  loadHistory() {
    const allHistory = app.getCalculationHistory();
    const recentHistory = allHistory.slice(0, 3).map(item => ({
      ...item,
      timeAgo: this.getTimeAgo(item.timestamp)
    }));
    
    const fullHistory = allHistory.map(item => ({
      ...item,
      timeAgo: this.getTimeAgo(item.timestamp),
      formatTime: this.formatTime(item.timestamp)
    }));
    
    this.setData({
      historyList: recentHistory,
      allHistoryList: fullHistory
    });
  },

  // 选择功能
  selectFunction(e) {
    const type = e.currentTarget.dataset.type;
    
    // 检查是否需要广告解锁
    if (type === 'workday' || type === 'holiday') {
      const userData = app.globalData.userData || {};
      if (!userData.premiumUnlocked) {
        this.setData({
          selectedFunction: type,
          showAdModal: true
        });
        return;
      }
    }
    
    // 直接跳转到计算页面
    this.navigateToCalculate(type);
  },

  // 跳转到计算页面
  navigateToCalculate(type) {
    tt.navigateTo({
      url: `/pages/calculate/calculate?type=${type}`
    });
  },

  // 快捷计算
  quickCalculate(e) {
    const type = e.currentTarget.dataset.type;
    const today = new Date();
    
    let result = '';
    let title = '';
    
    switch (type) {
      case 'today':
        result = `今天是${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
        title = '今日日期';
        break;
      case 'tomorrow':
        const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
        result = `明天是${tomorrow.getFullYear()}年${tomorrow.getMonth() + 1}月${tomorrow.getDate()}日`;
        title = '明日日期';
        break;
      case 'week':
        const weekStart = new Date(today.getTime() - today.getDay() * 24 * 60 * 60 * 1000);
        const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
        result = `本周从${weekStart.getMonth() + 1}月${weekStart.getDate()}日到${weekEnd.getMonth() + 1}月${weekEnd.getDate()}日`;
        title = '本周日期';
        break;
      case 'month':
        const monthDays = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
        const passedDays = today.getDate();
        const remainingDays = monthDays - passedDays;
        result = `本月共${monthDays}天，已过${passedDays}天，还剩${remainingDays}天`;
        title = '本月统计';
        break;
    }
    
    // 显示结果
    tt.showModal({
      title: title,
      content: result,
      showCancel: false,
      confirmText: '知道了'
    });
    
    // 记录计算历史
    this.recordCalculation({
      type: 'quick',
      title: title,
      description: `快捷查询 - ${type}`,
      result: result
    });
  },

  // 观看广告
  watchAd() {
    const self = this;
    
    app.showRewardedVideoAd((success) => {
      if (success) {
        // 解锁高级功能
        app.globalData.userData.premiumUnlocked = true;
        app.saveUserData();
        
        self.setData({
          showAdModal: false
        });
        
        // 跳转到选择的功能
        if (self.data.selectedFunction) {
          self.navigateToCalculate(self.data.selectedFunction);
        }
      }
    });
  },

  // 关闭广告弹窗
  closeAdModal() {
    this.setData({
      showAdModal: false,
      selectedFunction: null
    });
  },

  // 显示横幅广告
  showBannerAd() {
    app.showBannerAd();
    
    tt.showToast({
      title: '感谢支持！',
      icon: 'success'
    });
  },

  // 查看全部历史
  viewAllHistory() {
    this.setData({
      showHistoryModal: true
    });
  },

  // 关闭历史弹窗
  closeHistoryModal() {
    this.setData({
      showHistoryModal: false
    });
  },

  // 使用历史记录
  useHistory(e) {
    const item = e.currentTarget.dataset.item;
    
    tt.showModal({
      title: item.title,
      content: `${item.description}\n\n结果：${item.result}`,
      confirmText: '重新计算',
      cancelText: '知道了',
      success(res) {
        if (res.confirm) {
          // 根据历史记录类型跳转
          if (item.type !== 'quick') {
            tt.navigateTo({
              url: `/pages/calculate/calculate?type=${item.type}`
            });
          }
        }
      }
    });
  },

  // 清空历史
  clearHistory() {
    tt.showModal({
      title: '确认清空',
      content: '确定要清空所有计算历史吗？此操作不可恢复。',
      confirmText: '清空',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          app.clearCalculationHistory();
          this.setData({
            historyList: [],
            allHistoryList: [],
            showHistoryModal: false
          });
          
          tt.showToast({
            title: '已清空历史',
            icon: 'success'
          });
        }
      }
    });
  },

  // 删除单条历史
  deleteHistory(e) {
    const id = e.currentTarget.dataset.id;
    
    tt.showModal({
      title: '确认删除',
      content: '确定要删除这条计算记录吗？',
      confirmText: '删除',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 从历史记录中删除
          const userData = app.globalData.userData;
          userData.calculationHistory = userData.calculationHistory.filter(item => item.id !== id);
          app.saveUserData();
          
          // 重新加载历史
          this.loadHistory();
          
          tt.showToast({
            title: '已删除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 记录计算历史
  recordCalculation(calculation) {
    app.addCalculationHistory(calculation);
    
    // 更新今日计算次数
    const today = new Date().toDateString();
    const todayCalculations = this.data.todayCalculations + 1;
    
    tt.setStorageSync('dateCalc_lastCalculationDate', today);
    tt.setStorageSync('dateCalc_todayCalculations', todayCalculations);
    
    this.setData({
      todayCalculations: todayCalculations
    });
    
    // 重新加载数据
    this.loadUserData();
    this.loadHistory();
  },

  // 获取相对时间
  getTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    
    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;
    
    if (diff < minute) {
      return '刚刚';
    } else if (diff < hour) {
      return `${Math.floor(diff / minute)}分钟前`;
    } else if (diff < day) {
      return `${Math.floor(diff / hour)}小时前`;
    } else if (diff < 7 * day) {
      return `${Math.floor(diff / day)}天前`;
    } else {
      return this.formatTime(timestamp);
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  }
});