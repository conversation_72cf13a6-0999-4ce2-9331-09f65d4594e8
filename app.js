App({
  onLaunch(options) {
    console.log('日期计算器启动', options);
    
    // 初始化广告
    this.initAd();
    
    // 初始化用户数据
    this.initUserData();
  },

  onShow(options) {
    console.log('日期计算器显示', options);
  },

  onHide() {
    console.log('日期计算器隐藏');
  },

  onError(msg) {
    console.log('日期计算器错误', msg);
  },

  // 初始化广告
  initAd() {
    this.globalData.adConfig = {
      bannerId: 'banner_test_id',
      videoId: 'za8626ld0rc4pgsnif',
      interstitialId: 'interstitial_test_id'
    };
  },

  // 初始化用户数据
  initUserData() {
    const userData = tt.getStorageSync('dateCalc_userData') || {
      totalCalculations: 0,
      adWatchCount: 0,
      lastAdTime: 0,
      premiumUnlocked: false,
      calculationHistory: []
    };
    
    this.globalData.userData = userData;
    this.saveUserData();
  },

  // 保存用户数据
  saveUserData() {
    tt.setStorageSync('dateCalc_userData', this.globalData.userData);
  },

  // 显示激励视频广告
  showRewardedVideoAd(callback) {
    const self = this;

    // 创建激励视频广告实例
    const rewardedVideoAd = tt.createRewardedVideoAd({
      adUnitId: this.globalData.adConfig.videoId
    });

    // 监听广告加载成功
    rewardedVideoAd.onLoad(() => {
      console.log('激励视频广告加载成功');
    });

    // 监听广告加载失败
    rewardedVideoAd.onError((err) => {
      console.error('激励视频广告加载失败', err);
      tt.showToast({
        title: '广告加载失败，请稍后重试',
        icon: 'none'
      });
      if (callback) callback(false);
    });

    // 监听广告关闭
    rewardedVideoAd.onClose((res) => {
      if (res && res.isEnded) {
        // 用户看完广告
        self.globalData.userData.adWatchCount++;
        self.globalData.userData.lastAdTime = Date.now();
        self.saveUserData();

        // 计算收益
        self.calculateAdRevenue('video');

        tt.showToast({
          title: '功能已解锁',
          icon: 'success'
        });

        if (callback) callback(true);
      } else {
        // 用户中途关闭广告
        tt.showToast({
          title: '请观看完整广告',
          icon: 'none'
        });
        if (callback) callback(false);
      }
    });

    // 显示广告
    rewardedVideoAd.show().catch(() => {
      // 广告显示失败，可能是广告还没加载好
      rewardedVideoAd.load().then(() => {
        return rewardedVideoAd.show();
      }).catch((err) => {
        console.error('激励视频广告显示失败', err);
        tt.showToast({
          title: '广告显示失败',
          icon: 'none'
        });
        if (callback) callback(false);
      });
    });
  },

  // 显示横幅广告
  showBannerAd() {
    console.log('显示横幅广告');

    // 创建横幅广告实例
    if (!this.globalData.bannerAd) {
      this.globalData.bannerAd = tt.createBannerAd({
        adUnitId: this.globalData.adConfig.bannerId,
        style: {
          left: 0,
          top: 0,
          width: 375
        }
      });

      // 监听广告加载成功
      this.globalData.bannerAd.onLoad(() => {
        console.log('横幅广告加载成功');
      });

      // 监听广告加载失败
      this.globalData.bannerAd.onError((err) => {
        console.error('横幅广告加载失败', err);
      });

      // 监听广告尺寸变化
      this.globalData.bannerAd.onResize((res) => {
        console.log('横幅广告尺寸变化', res);
      });
    }

    // 显示横幅广告
    this.globalData.bannerAd.show().then(() => {
      this.calculateAdRevenue('banner');
    }).catch((err) => {
      console.error('横幅广告显示失败', err);
    });
  },

  // 隐藏横幅广告
  hideBannerAd() {
    if (this.globalData.bannerAd) {
      this.globalData.bannerAd.hide();
    }
  },

  // 显示插屏广告
  showInterstitialAd() {
    const self = this;
    
    tt.showModal({
      title: '广告',
      content: '这是一个插屏广告示例',
      showCancel: false,
      confirmText: '关闭',
      success() {
        self.calculateAdRevenue('interstitial');
      }
    });
  },

  // 计算广告收益
  calculateAdRevenue(adType) {
    const rates = {
      video: 0.4,      // 激励视频
      banner: 0.08,    // 横幅广告
      interstitial: 0.25 // 插屏广告
    };
    
    const revenue = rates[adType] || 0;
    const totalRevenue = tt.getStorageSync('dateCalc_totalRevenue') || 0;
    
    tt.setStorageSync('dateCalc_totalRevenue', totalRevenue + revenue);
    
    console.log(`广告收益: ${adType} +${revenue}元, 总收益: ${totalRevenue + revenue}元`);
  },

  // 检查是否需要显示广告
  shouldShowAd() {
    const userData = this.globalData.userData;
    const now = Date.now();
    const timeSinceLastAd = now - userData.lastAdTime;
    
    // 每5分钟最多显示一次广告
    return timeSinceLastAd > 5 * 60 * 1000;
  },

  // 添加计算历史
  addCalculationHistory(calculation) {
    const userData = this.globalData.userData;
    userData.calculationHistory.unshift({
      ...calculation,
      timestamp: Date.now(),
      id: Date.now().toString()
    });
    
    // 最多保存50条历史记录
    if (userData.calculationHistory.length > 50) {
      userData.calculationHistory = userData.calculationHistory.slice(0, 50);
    }
    
    userData.totalCalculations++;
    this.saveUserData();
  },

  // 获取计算历史
  getCalculationHistory() {
    return this.globalData.userData.calculationHistory || [];
  },

  // 清除计算历史
  clearCalculationHistory() {
    this.globalData.userData.calculationHistory = [];
    this.saveUserData();
  },

  // 全局数据
  globalData: {
    userData: null,
    adConfig: null,
    currentCalculation: null,
    bannerAd: null
  }
});