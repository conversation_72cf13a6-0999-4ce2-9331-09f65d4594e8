.container {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
}

/* 顶部标题 */
.header {
  text-align: center;
  margin-bottom: 32px;
  padding: 20px 0;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: #1E293B;
  display: block;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #64748B;
  display: block;
}

/* 功能卡片 */
.function-cards {
  margin-bottom: 32px;
}

.function-card {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 16px;
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.function-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s;
}

.function-card:active {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.function-card:active::before {
  left: 100%;
}

.card-icon {
  font-size: 32px;
  margin-right: 16px;
  width: 48px;
  text-align: center;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
  margin-bottom: 4px;
  display: block;
}

.card-desc {
  font-size: 14px;
  color: #64748B;
  line-height: 1.4;
  display: block;
}

.card-arrow {
  font-size: 20px;
  color: #3B82F6;
  margin-left: 12px;
}

.premium-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* 快捷计算 */
.quick-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.title-text {
  font-size: 20px;
  font-weight: bold;
  color: #1E293B;
}

.title-desc {
  font-size: 14px;
  color: #64748B;
}

.title-action {
  font-size: 14px;
  color: #3B82F6;
  font-weight: 500;
}

.quick-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.quick-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.quick-item:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.quick-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.quick-text {
  font-size: 16px;
  font-weight: 500;
  color: #1E293B;
  margin-bottom: 4px;
  display: block;
}

.quick-date {
  font-size: 12px;
  color: #64748B;
  display: block;
}

/* 历史记录 */
.history-section {
  margin-bottom: 32px;
}

.history-list {
  background: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.history-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #F1F5F9;
  transition: background 0.2s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: #F8FAFC;
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.history-title {
  font-size: 16px;
  font-weight: 500;
  color: #1E293B;
  margin-bottom: 4px;
  display: block;
}

.history-desc {
  font-size: 14px;
  color: #64748B;
  margin-bottom: 4px;
  display: block;
}

.history-result {
  font-size: 12px;
  color: #3B82F6;
  display: block;
}

.history-time {
  font-size: 12px;
  color: #9CA3AF;
  margin-left: 12px;
}

/* 统计信息 */
.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stats-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #3B82F6;
  margin-bottom: 4px;
  display: block;
}

.stats-label {
  font-size: 12px;
  color: #64748B;
  display: block;
  text-align: center;
}

/* 广告横幅 */
.ad-banner {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.ad-banner:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.ad-text {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
}

/* 广告弹窗 */
.ad-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.ad-modal-content {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin: 20px;
  max-width: 320px;
  width: 100%;
  text-align: center;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.ad-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.ad-header {
  margin-bottom: 24px;
}

.ad-title {
  font-size: 20px;
  font-weight: bold;
  color: #1E293B;
  margin-bottom: 8px;
  display: block;
}

.ad-subtitle {
  font-size: 14px;
  color: #64748B;
  line-height: 1.5;
  display: block;
}

.ad-benefits {
  margin-bottom: 24px;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  text-align: left;
}

.benefit-icon {
  font-size: 16px;
  margin-right: 12px;
  color: #10B981;
}

.benefit-text {
  font-size: 14px;
  color: #374151;
}

.ad-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ad-buttons .btn-primary,
.ad-buttons .btn-secondary {
  width: 100%;
  margin: 0;
}

/* 历史记录弹窗 */
.history-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.history-modal-content {
  background: #FFFFFF;
  border-radius: 16px;
  margin: 20px;
  max-width: 400px;
  width: 100%;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
}

.history-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #E2E8F0;
  margin-bottom: 0;
}

.history-title {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
}

.history-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.clear-history-btn {
  color: #EF4444;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.1);
}

.clear-history-btn:active {
  background: rgba(239, 68, 68, 0.2);
}

.close-btn {
  color: #64748B;
  font-size: 20px;
  padding: 4px;
  border-radius: 4px;
  background: #F1F5F9;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:active {
  background: #E2E8F0;
}

.history-list-full {
  flex: 1;
  padding: 20px;
  max-height: 400px;
}

.history-item-full {
  display: flex;
  align-items: center;
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  transition: background 0.2s ease;
}

.history-item-full:active {
  background: #F1F5F9;
}

.history-content-full {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.history-title-full {
  font-size: 16px;
  font-weight: 500;
  color: #1E293B;
  margin-bottom: 4px;
  display: block;
}

.history-desc-full {
  font-size: 14px;
  color: #64748B;
  margin-bottom: 4px;
  display: block;
}

.history-result-full {
  font-size: 12px;
  color: #3B82F6;
  margin-bottom: 4px;
  display: block;
}

.history-time-full {
  font-size: 12px;
  color: #9CA3AF;
  display: block;
}

.history-delete {
  margin-left: 12px;
  padding: 8px;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.1);
}

.history-delete:active {
  background: rgba(239, 68, 68, 0.2);
}

.delete-icon {
  font-size: 16px;
}

.empty-history {
  text-align: center;
  padding: 40px 20px;
  color: #9CA3AF;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
  display: block;
}

.empty-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #6B7280;
  display: block;
}

.empty-description {
  font-size: 14px;
  line-height: 1.5;
  display: block;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 16px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .function-card {
    padding: 16px;
  }
  
  .card-icon {
    font-size: 28px;
    width: 40px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .quick-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-number {
    font-size: 20px;
  }
}