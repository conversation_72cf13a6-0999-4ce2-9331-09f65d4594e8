.container {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 12px 0;
}

.nav-back {
  padding: 8px;
  border-radius: 8px;
  background: #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.nav-back:active {
  transform: scale(0.95);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.back-icon {
  font-size: 20px;
  color: #3B82F6;
  font-weight: bold;
}

.nav-title {
  font-size: 20px;
  font-weight: bold;
  color: #1E293B;
  flex: 1;
  text-align: center;
}

.nav-action {
  padding: 8px;
  border-radius: 8px;
  background: #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.nav-action:active {
  transform: scale(0.95);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 18px;
  color: #64748B;
}

/* 计算区域 */
.calc-section {
  margin-bottom: 32px;
}

.section-title {
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.title-text {
  font-size: 24px;
  font-weight: bold;
  color: #1E293B;
  display: block;
  margin-bottom: 8px;
}

.title-desc {
  font-size: 16px;
  color: #64748B;
  display: block;
}

/* 输入组 */
.input-group {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #FFFFFF;
  border: 2px solid #E2E8F0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.date-picker:active {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.date-text {
  font-size: 16px;
  color: #1E293B;
  flex: 1;
}

.picker-icon {
  font-size: 20px;
  color: #3B82F6;
}

/* 操作选择 */
.operation-group {
  margin-bottom: 20px;
}

.operation-tabs {
  display: flex;
  background: #F1F5F9;
  border-radius: 12px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  font-size: 16px;
  color: #64748B;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-item.active {
  background: #3B82F6;
  color: #FFFFFF;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.tab-text {
  display: block;
}

/* 时间输入 */
.time-inputs {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.time-input-group {
  flex: 1;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  text-align: center;
}

.time-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #64748B;
  margin-bottom: 8px;
}

.time-input {
  width: 100%;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
  border: none;
  background: transparent;
  outline: none;
}

/* 选项组 */
.options-group {
  margin-bottom: 20px;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #F1F5F9;
}

.option-item:last-child {
  border-bottom: none;
}

.option-label {
  font-size: 16px;
  color: #1E293B;
  font-weight: 500;
}

/* 结果区域 */
.result-section {
  margin-top: 24px;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-card {
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  border: 2px solid #10B981;
}

.result-header {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  padding: 20px;
  text-align: center;
}

.result-title {
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
}

.result-content {
  padding: 20px;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #F1F5F9;
}

.result-item:last-child {
  border-bottom: none;
}

.result-label {
  font-size: 16px;
  color: #64748B;
  font-weight: 500;
}

.result-value {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
}

/* 高级功能提示 */
.premium-notice {
  background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
  border: 2px solid #F59E0B;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  margin-bottom: 24px;
  animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.notice-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.notice-text {
  font-size: 18px;
  font-weight: 500;
  color: #92400E;
  margin-bottom: 16px;
  display: block;
}

/* 节假日列表 */
.holiday-list {
  margin-top: 24px;
  background: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.holiday-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #F1F5F9;
  transition: background 0.2s ease;
}

.holiday-item:last-child {
  border-bottom: none;
}

.holiday-item:active {
  background: #F8FAFC;
}

.holiday-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.holiday-name {
  font-size: 18px;
  font-weight: bold;
  color: #1E293B;
  margin-bottom: 4px;
  display: block;
}

.holiday-date {
  font-size: 14px;
  color: #64748B;
  margin-bottom: 4px;
  display: block;
}

.holiday-type {
  font-size: 12px;
  color: #3B82F6;
  background: rgba(59, 130, 246, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.holiday-days {
  text-align: right;
}

.days-text {
  font-size: 16px;
  font-weight: bold;
  color: #10B981;
  background: rgba(16, 185, 129, 0.1);
  padding: 8px 12px;
  border-radius: 20px;
}

/* 广告横幅 */
.ad-banner {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  margin-top: 32px;
}

.ad-banner:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.ad-text {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 16px;
  }
  
  .nav-title {
    font-size: 18px;
  }
  
  .title-text {
    font-size: 20px;
  }
  
  .time-inputs {
    flex-direction: column;
    gap: 8px;
  }
  
  .time-input-group {
    padding: 12px;
  }
  
  .result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .result-value {
    font-size: 16px;
  }
  
  .holiday-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .holiday-days {
    align-self: flex-end;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: #CBD5E1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94A3B8;
}